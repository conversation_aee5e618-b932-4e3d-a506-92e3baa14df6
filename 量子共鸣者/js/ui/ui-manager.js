/**
 * 量子共鸣者 - UI管理器
 * 负责管理所有用户界面组件、屏幕切换、动画效果
 */

class UIManager {
    constructor() {
        this.currentScreen = 'loading';
        this.previousScreen = null;
        this.isTransitioning = false;
        this.transitionDuration = 500; // 毫秒
        
        // UI组件注册表
        this.components = new Map();
        this.screens = new Map();
        
        // 动画队列
        this.animationQueue = [];
        this.isAnimating = false;
        
        // 响应式断点
        this.breakpoints = {
            mobile: 768,
            tablet: 1024,
            desktop: 1440
        };
        
        // 当前设备类型
        this.deviceType = this.getDeviceType();
        
        // 主题设置
        this.theme = {
            primary: '#00ffff',
            secondary: '#0080ff',
            accent: '#ff00ff',
            background: '#0a0a0f',
            surface: '#1a1a2e',
            text: '#ffffff',
            textSecondary: '#cccccc'
        };
        
        // UI状态
        this.state = {
            menuOpen: false,
            settingsOpen: false,
            gameHUDVisible: false,
            modalOpen: false,
            fullscreen: false
        };
        
        console.log('🎨 UI管理器已创建');
    }

    /**
     * 初始化UI管理器
     */
    init() {
        try {
            // 注册所有屏幕
            this.registerScreens();
            
            // 注册UI组件
            this.registerComponents();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化响应式系统
            this.initResponsiveSystem();
            
            // 应用主题
            this.applyTheme();
            
            // 设置初始屏幕
            this.showScreen('loading');
            
            console.log('✅ UI管理器初始化完成');
            return true;
        } catch (error) {
            console.error('❌ UI管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 注册所有屏幕
     */
    registerScreens() {
        const screenElements = [
            'loadingScreen',
            'mainMenuScreen',
            'gameScreen',
            'pauseScreen',
            'settingsScreen',
            'playerSelectionScreen',
            'gameOverScreen',
            'levelSelectScreen',
            'levelEditorScreen',
            'achievementsScreen',
            'leaderboardScreen'
        ];

        screenElements.forEach(screenId => {
            const element = document.getElementById(screenId);
            if (element) {
                this.screens.set(screenId, {
                    element: element,
                    visible: false,
                    initialized: false
                });
            }
        });

        console.log(`📱 已注册 ${this.screens.size} 个屏幕`);
    }

    /**
     * 注册UI组件
     */
    registerComponents() {
        // 按钮组件
        this.registerButtonComponents();
        
        // 滑块组件
        this.registerSliderComponents();
        
        // 模态框组件
        this.registerModalComponents();
        
        // HUD组件
        this.registerHUDComponents();
        
        // 菜单组件
        this.registerMenuComponents();

        // 设置按钮事件处理
        this.setupButtonEvents();
    }

    /**
     * 设置按钮事件处理
     */
    setupButtonEvents() {
        // 注意：开始游戏按钮的事件处理已在 main.js 中设置
        // 这里不再重复绑定以避免事件冲突

        // 关卡编辑器按钮
        const levelEditorBtn = document.getElementById('level-editor-btn');
        if (levelEditorBtn) {
            levelEditorBtn.addEventListener('click', () => {
                if (window.levelEditor) {
                    levelEditor.show();
                }
            });
        }

        // 设置按钮
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showScreen('settingsScreen');
            });
        }

        // 自定义关卡按钮
        const customLevelsBtn = document.getElementById('custom-levels-btn');
        if (customLevelsBtn) {
            customLevelsBtn.addEventListener('click', () => {
                // 显示自定义关卡列表
                this.showCustomLevels();
            });
        }

        // 排行榜按钮
        const leaderboardBtn = document.getElementById('leaderboard-btn');
        if (leaderboardBtn) {
            leaderboardBtn.addEventListener('click', () => {
                if (window.leaderboardUI) {
                    leaderboardUI.show();
                }
            });
        }

        // 成就按钮
        const achievementsBtn = document.getElementById('achievements-btn');
        if (achievementsBtn) {
            achievementsBtn.addEventListener('click', () => {
                if (window.achievementsUI) {
                    achievementsUI.show();
                }
            });
        }

        // 切换玩家按钮
        const switchPlayerBtn = document.getElementById('switch-player-btn');
        if (switchPlayerBtn) {
            switchPlayerBtn.addEventListener('click', () => {
                this.showScreen('playerSelectionScreen');
            });
        }

        // 语言切换按钮
        const langBtn = document.getElementById('lang-btn');
        if (langBtn) {
            langBtn.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
    }

    /**
     * 显示自定义关卡列表
     */
    showCustomLevels() {
        if (window.levelManager) {
            const customLevels = levelManager.getLevelList(true).filter(level => level.type === 'custom');

            if (customLevels.length === 0) {
                this.showModal('info', {
                    title: '自定义关卡',
                    content: '暂无自定义关卡，请先使用关卡编辑器创建关卡。',
                    buttons: [{ text: '确定', action: 'close' }]
                });
                return;
            }

            // 显示自定义关卡选择界面
            this.showModal('customLevels', {
                title: '自定义关卡',
                content: this.generateCustomLevelsHTML(customLevels),
                buttons: [{ text: '关闭', action: 'close' }]
            });
        }
    }

    /**
     * 生成自定义关卡HTML
     * @param {Array} levels - 关卡列表
     * @returns {string} HTML字符串
     */
    generateCustomLevelsHTML(levels) {
        return `
            <div class="custom-levels-list">
                ${levels.map(level => `
                    <div class="custom-level-item" data-level-id="${level.id}">
                        <div class="level-info">
                            <h4>${level.name}</h4>
                            <p>${level.description}</p>
                            <small>作者: ${level.author}</small>
                        </div>
                        <div class="level-actions">
                            <button class="play-level-btn" data-level-id="${level.id}">游玩</button>
                            <button class="edit-level-btn" data-level-id="${level.id}">编辑</button>
                            <button class="delete-level-btn" data-level-id="${level.id}">删除</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 显示排行榜
     */
    showLeaderboard() {
        if (window.leaderboardUI) {
            leaderboardUI.show();
        } else {
            this.showModal('leaderboard', {
                title: '排行榜',
                content: '排行榜功能正在加载中...',
                buttons: [{ text: '确定', action: 'close' }]
            });
        }
    }

    /**
     * 切换语言
     */
    toggleLanguage() {
        if (window.i18n) {
            const currentLang = i18n.getCurrentLanguage();
            const newLang = currentLang === 'zh' ? 'en' : 'zh';
            i18n.setLanguage(newLang);

            // 更新语言显示
            const langText = document.getElementById('current-lang');
            if (langText) {
                langText.textContent = newLang === 'zh' ? '中文' : 'English';
            }
        }
    }

    /**
     * 显示成就解锁通知
     * @param {Object} achievement - 成就数据
     */
    showAchievementToast(achievement) {
        if (window.achievementsUI) {
            achievementsUI.showAchievementToast(achievement);
        } else {
            // 备用简单通知
            this.showToast(`🏆 成就解锁: ${achievement.name}`);
        }
    }

    /**
     * 显示简单通知
     * @param {string} message - 通知消息
     * @param {number} duration - 显示时长（毫秒）
     */
    showToast(message, duration = 3000) {
        // 创建通知元素
        const toast = document.createElement('div');
        toast.className = 'ui-toast';
        toast.textContent = message;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 500);
        }, duration);
    }

    /**
     * 注册按钮组件
     */
    registerButtonComponents() {
        const buttons = document.querySelectorAll('.quantum-button, .menu-button, .game-button');
        
        buttons.forEach(button => {
            this.components.set(button.id || `button_${Date.now()}_${Math.random()}`, {
                type: 'button',
                element: button,
                events: ['click', 'hover']
            });
            
            // 添加按钮效果
            this.enhanceButton(button);
        });
    }

    /**
     * 增强按钮效果
     * @param {HTMLElement} button - 按钮元素
     */
    enhanceButton(button) {
        // 添加量子发光效果
        button.addEventListener('mouseenter', () => {
            button.classList.add('quantum-glow');
            this.playUISound('hover');
        });
        
        button.addEventListener('mouseleave', () => {
            button.classList.remove('quantum-glow');
        });
        
        button.addEventListener('click', (e) => {
            this.createRippleEffect(e);
            this.playUISound('click');
        });
        
        // 添加键盘支持
        button.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    }

    /**
     * 创建涟漪效果
     * @param {Event} event - 点击事件
     */
    createRippleEffect(event) {
        const button = event.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.className = 'quantum-ripple';
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        
        button.appendChild(ripple);
        
        // 移除涟漪效果
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    /**
     * 注册滑块组件
     */
    registerSliderComponents() {
        const sliders = document.querySelectorAll('.quantum-slider, .volume-slider');
        
        sliders.forEach(slider => {
            this.components.set(slider.id || `slider_${Date.now()}_${Math.random()}`, {
                type: 'slider',
                element: slider,
                events: ['input', 'change']
            });
            
            this.enhanceSlider(slider);
        });
    }

    /**
     * 增强滑块效果
     * @param {HTMLElement} slider - 滑块元素
     */
    enhanceSlider(slider) {
        // 添加量子轨道效果
        const track = document.createElement('div');
        track.className = 'quantum-track';
        slider.parentNode.insertBefore(track, slider);
        
        // 更新轨道效果
        const updateTrack = () => {
            const value = (slider.value - slider.min) / (slider.max - slider.min);
            track.style.background = `linear-gradient(90deg, 
                ${this.theme.primary} 0%, 
                ${this.theme.secondary} ${value * 100}%, 
                rgba(255,255,255,0.1) ${value * 100}%)`;
        };
        
        slider.addEventListener('input', updateTrack);
        updateTrack();
    }

    /**
     * 注册模态框组件
     */
    registerModalComponents() {
        const modals = document.querySelectorAll('.modal, .quantum-modal');
        
        modals.forEach(modal => {
            this.components.set(modal.id || `modal_${Date.now()}_${Math.random()}`, {
                type: 'modal',
                element: modal,
                events: ['show', 'hide']
            });
        });
    }

    /**
     * 注册HUD组件
     */
    registerHUDComponents() {
        const hudElements = [
            'scoreDisplay',
            'energyBar',
            'frequencyDisplay',
            'comboCounter',
            'timeDisplay',
            'particleCounter'
        ];

        hudElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                this.components.set(elementId, {
                    type: 'hud',
                    element: element,
                    events: ['update']
                });
            }
        });
    }

    /**
     * 注册菜单组件
     */
    registerMenuComponents() {
        const menus = document.querySelectorAll('.menu, .dropdown-menu');
        
        menus.forEach(menu => {
            this.components.set(menu.id || `menu_${Date.now()}_${Math.random()}`, {
                type: 'menu',
                element: menu,
                events: ['open', 'close']
            });
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // 全屏变化
        document.addEventListener('fullscreenchange', () => this.handleFullscreenChange());
        
        // 设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleResize(), 100);
        });
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        const newDeviceType = this.getDeviceType();
        
        if (newDeviceType !== this.deviceType) {
            this.deviceType = newDeviceType;
            this.onDeviceTypeChange();
        }
        
        // 更新响应式布局
        this.updateResponsiveLayout();
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeydown(event) {
        switch (event.key) {
            case 'Escape':
                this.handleEscapeKey();
                break;
            case 'F11':
                event.preventDefault();
                this.toggleFullscreen();
                break;
            case 'Tab':
                this.handleTabNavigation(event);
                break;
        }
    }

    /**
     * 处理ESC键
     */
    handleEscapeKey() {
        if (this.state.modalOpen) {
            this.hideModal();
        } else if (this.currentScreen === 'gameScreen') {
            this.showPauseMenu();
        } else if (this.currentScreen === 'pauseScreen') {
            this.resumeGame();
        }
    }

    /**
     * 处理Tab导航
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleTabNavigation(event) {
        // 获取当前屏幕中可聚焦的元素
        const currentScreen = this.screens.get(this.currentScreen);
        if (!currentScreen || !currentScreen.element) return;

        const focusableElements = currentScreen.element.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (event.shiftKey) {
            // Shift + Tab (向后)
            if (document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab (向前)
            if (document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * 处理全屏变化
     */
    handleFullscreenChange() {
        this.state.fullscreen = !!document.fullscreenElement;

        // 更新全屏按钮状态
        const fullscreenButtons = document.querySelectorAll('.fullscreen-button');
        fullscreenButtons.forEach(button => {
            button.classList.toggle('fullscreen-active', this.state.fullscreen);
        });

        // 触发布局更新
        setTimeout(() => this.handleResize(), 100);
    }

    /**
     * 切换全屏
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('⚠️ 无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen().catch(err => {
                console.warn('⚠️ 无法退出全屏模式:', err);
            });
        }
    }

    /**
     * 显示暂停菜单
     */
    showPauseMenu() {
        if (window.gameController) {
            gameController.pauseGame();
        }

        this.showScreen('pauseScreen');
        this.state.menuOpen = true;
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        if (window.gameController) {
            gameController.resumeGame();
        }

        this.showScreen('gameScreen');
        this.state.menuOpen = false;
    }

    /**
     * 显示模态框
     * @param {string} modalId - 模态框ID
     * @param {Object} options - 选项
     */
    showModal(modalId, options = {}) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`❌ 模态框不存在: ${modalId}`);
            return;
        }

        // 设置模态框内容
        if (options.title) {
            const titleElement = modal.querySelector('.modal-title');
            if (titleElement) titleElement.textContent = options.title;
        }

        if (options.content) {
            const contentElement = modal.querySelector('.modal-content');
            if (contentElement) contentElement.innerHTML = options.content;
        }

        // 显示模态框
        modal.style.display = 'flex';
        modal.classList.add('modal-enter');

        setTimeout(() => {
            modal.classList.remove('modal-enter');
        }, 300);

        this.state.modalOpen = true;

        // 设置关闭事件
        const closeButtons = modal.querySelectorAll('.modal-close, .modal-cancel');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => this.hideModal(modalId), { once: true });
        });

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal(modalId);
            }
        }, { once: true });
    }

    /**
     * 隐藏模态框
     * @param {string} modalId - 模态框ID
     */
    hideModal(modalId) {
        const modal = modalId ? document.getElementById(modalId) : document.querySelector('.modal[style*="flex"]');
        if (!modal) return;

        modal.classList.add('modal-exit');

        setTimeout(() => {
            modal.style.display = 'none';
            modal.classList.remove('modal-exit');
        }, 300);

        this.state.modalOpen = false;
    }

    /**
     * 显示屏幕
     * @param {string} screenName - 屏幕名称
     * @param {Object} options - 选项
     */
    async showScreen(screenName, options = {}) {
        if (this.isTransitioning) {
            console.warn('⚠️ 屏幕切换进行中，忽略请求');
            return;
        }

        const screen = this.screens.get(screenName);
        if (!screen) {
            console.error(`❌ 屏幕不存在: ${screenName}`);
            return;
        }

        this.isTransitioning = true;
        this.previousScreen = this.currentScreen;

        try {
            // 隐藏当前屏幕
            if (this.currentScreen && this.currentScreen !== screenName) {
                await this.hideScreen(this.currentScreen);
            }

            // 显示新屏幕
            await this.displayScreen(screenName, options);
            
            this.currentScreen = screenName;
            this.isTransitioning = false;

            console.log(`📱 屏幕切换完成: ${this.previousScreen} → ${screenName}`);
        } catch (error) {
            console.error('❌ 屏幕切换失败:', error);
            this.isTransitioning = false;
        }
    }

    /**
     * 隐藏屏幕
     * @param {string} screenName - 屏幕名称
     */
    async hideScreen(screenName) {
        const screen = this.screens.get(screenName);
        if (!screen || !screen.visible) return;

        return new Promise(resolve => {
            screen.element.classList.add('screen-exit');
            
            setTimeout(() => {
                screen.element.style.display = 'none';
                screen.element.classList.remove('screen-exit');
                screen.visible = false;
                resolve();
            }, this.transitionDuration);
        });
    }

    /**
     * 显示屏幕
     * @param {string} screenName - 屏幕名称
     * @param {Object} options - 选项
     */
    async displayScreen(screenName, options) {
        const screen = this.screens.get(screenName);
        if (!screen) return;

        return new Promise(resolve => {
            screen.element.style.display = 'flex';
            screen.element.classList.add('screen-enter');
            
            // 初始化屏幕（如果需要）
            if (!screen.initialized) {
                this.initializeScreen(screenName, options);
                screen.initialized = true;
            }
            
            setTimeout(() => {
                screen.element.classList.remove('screen-enter');
                screen.visible = true;
                resolve();
            }, this.transitionDuration);
        });
    }

    /**
     * 初始化屏幕
     * @param {string} screenName - 屏幕名称
     * @param {Object} options - 选项
     */
    initializeScreen(screenName, options) {
        switch (screenName) {
            case 'mainMenuScreen':
                this.initMainMenu(options);
                break;
            case 'gameScreen':
                this.initGameScreen(options);
                break;
            case 'settingsScreen':
                this.initSettingsScreen(options);
                break;
            case 'playerSelectionScreen':
                this.initPlayerSelection(options);
                break;
        }
    }

    /**
     * 初始化主菜单
     * @param {Object} options - 选项
     */
    initMainMenu(options) {
        console.log('🏠 初始化主菜单');

        // 设置背景动画
        this.setupMenuBackground();

        // 设置菜单按钮动画
        this.setupMenuButtonAnimations();
    }

    /**
     * 初始化游戏屏幕
     * @param {Object} options - 选项
     */
    initGameScreen(options) {
        console.log('🎮 初始化游戏屏幕');

        // 初始化游戏HUD
        if (window.gameHUD && !window.gameHUD.isInitialized) {
            window.gameHUD.init();
        }

        // 显示HUD
        if (window.gameHUD) {
            window.gameHUD.show();
        }

        this.state.gameHUDVisible = true;
    }

    /**
     * 初始化设置屏幕
     * @param {Object} options - 选项
     */
    initSettingsScreen(options) {
        console.log('⚙️ 初始化设置屏幕');

        // 加载当前设置
        this.loadCurrentSettings();

        // 设置设置项动画
        this.setupSettingsAnimations();
    }

    /**
     * 初始化玩家选择屏幕
     * @param {Object} options - 选项
     */
    initPlayerSelection(options) {
        console.log('👤 初始化玩家选择屏幕');

        // 设置玩家选择动画
        this.setupPlayerSelectionAnimations();
    }

    /**
     * 设置菜单背景动画
     */
    setupMenuBackground() {
        const menuScreen = this.screens.get('mainMenuScreen');
        if (menuScreen && menuScreen.element) {
            menuScreen.element.classList.add('menu-background-animated');
        }
    }

    /**
     * 设置菜单按钮动画
     */
    setupMenuButtonAnimations() {
        const menuButtons = document.querySelectorAll('.main-menu .menu-button');

        menuButtons.forEach((button, index) => {
            // 延迟显示动画
            setTimeout(() => {
                button.classList.add('button-enter');
            }, index * 100);
        });
    }

    /**
     * 加载当前设置
     */
    loadCurrentSettings() {
        if (window.storageService) {
            const settings = storageService.getSettings();
            this.applySettingsToUI(settings);
        }
    }

    /**
     * 应用设置到UI
     * @param {Object} settings - 设置对象
     */
    applySettingsToUI(settings) {
        // 音量设置
        const volumeSlider = document.getElementById('volumeSlider');
        if (volumeSlider && settings.audio) {
            volumeSlider.value = settings.audio.masterVolume * 100;
        }

        // 语言设置
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect && settings.language) {
            languageSelect.value = settings.language;
        }

        // 渲染质量设置
        const qualitySelect = document.getElementById('qualitySelect');
        if (qualitySelect && settings.graphics) {
            qualitySelect.value = settings.graphics.quality;
        }
    }

    /**
     * 设置设置项动画
     */
    setupSettingsAnimations() {
        const settingsItems = document.querySelectorAll('.settings-item');

        settingsItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('settings-item-enter');
            }, index * 50);
        });
    }

    /**
     * 设置玩家选择动画
     */
    setupPlayerSelectionAnimations() {
        const playerCards = document.querySelectorAll('.player-card');

        playerCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('card-enter');
            }, index * 100);
        });
    }

    /**
     * 获取设备类型
     * @returns {string} 设备类型
     */
    getDeviceType() {
        const width = window.innerWidth;
        
        if (width < this.breakpoints.mobile) {
            return 'mobile';
        } else if (width < this.breakpoints.tablet) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    /**
     * 设备类型变化处理
     */
    onDeviceTypeChange() {
        document.body.className = document.body.className.replace(/device-\w+/g, '');
        document.body.classList.add(`device-${this.deviceType}`);
        
        console.log(`📱 设备类型变化: ${this.deviceType}`);
    }

    /**
     * 初始化响应式系统
     */
    initResponsiveSystem() {
        this.onDeviceTypeChange();
        this.updateResponsiveLayout();
    }

    /**
     * 更新响应式布局
     */
    updateResponsiveLayout() {
        // 根据设备类型调整布局
        const root = document.documentElement;
        
        switch (this.deviceType) {
            case 'mobile':
                root.style.setProperty('--ui-scale', '0.8');
                root.style.setProperty('--button-size', '44px');
                root.style.setProperty('--font-size-base', '14px');
                break;
            case 'tablet':
                root.style.setProperty('--ui-scale', '0.9');
                root.style.setProperty('--button-size', '48px');
                root.style.setProperty('--font-size-base', '16px');
                break;
            case 'desktop':
                root.style.setProperty('--ui-scale', '1.0');
                root.style.setProperty('--button-size', '52px');
                root.style.setProperty('--font-size-base', '18px');
                break;
        }
    }

    /**
     * 应用主题
     */
    applyTheme() {
        const root = document.documentElement;
        
        Object.entries(this.theme).forEach(([key, value]) => {
            root.style.setProperty(`--color-${key}`, value);
        });
    }

    /**
     * 播放UI音效
     * @param {string} soundType - 音效类型
     */
    playUISound(soundType) {
        if (window.audioManager) {
            switch (soundType) {
                case 'click':
                    audioManager.playUISound('click');
                    break;
                case 'hover':
                    audioManager.playUISound('hover');
                    break;
                case 'error':
                    audioManager.playUISound('error');
                    break;
                case 'success':
                    audioManager.playUISound('success');
                    break;
            }
        }
    }

    /**
     * 获取当前屏幕
     * @returns {string} 当前屏幕名称
     */
    getCurrentScreen() {
        return this.currentScreen;
    }

    /**
     * 获取UI状态
     * @returns {Object} UI状态
     */
    getState() {
        return { ...this.state };
    }

    /**
     * 销毁UI管理器
     */
    destroy() {
        // 清理事件监听器
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('keydown', this.handleKeydown);
        document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
        
        // 清理组件
        this.components.clear();
        this.screens.clear();
        
        console.log('🎨 UI管理器已销毁');
    }
}

// 导出类到全局作用域
window.UIManager = UIManager;

// 创建全局UI管理器实例
window.uiManager = new UIManager();
